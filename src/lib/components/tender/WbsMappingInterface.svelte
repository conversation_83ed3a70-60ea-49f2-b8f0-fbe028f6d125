<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Card from '$lib/components/ui/card';
	import * as Dialog from '$lib/components/ui/dialog';
	import { Badge } from '$lib/components/ui/badge';
	import WbsTreeSelector from './WbsTreeSelector.svelte';
	import CoverageInput from './CoverageInput.svelte';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import TrashIcon from 'phosphor-svelte/lib/Trash';
	import PencilIcon from 'phosphor-svelte/lib/Pencil';
	import type { WbsItemWithBudget, TenderWbsMappingRow } from '$lib/schemas/tender';
	import { formatCurrency } from '$lib/utils';
	import { SvelteMap } from 'svelte/reactivity';

	interface Props {
		lineItemId: string;
		lineItemDescription: string;
		availableWbsItems: WbsItemWithBudget[];
		existingMappings: (TenderWbsMappingRow & {
			wbs_library_item: {
				code: string;
				description: string;
				level: number;
			};
		})[];
		currencySymbol?: string;
		symbolPosition?: 'before' | 'after';
		disabled?: boolean;
	}

	let {
		lineItemId,
		lineItemDescription,
		availableWbsItems,
		existingMappings = $bindable([]),
		currencySymbol = 'kr',
		symbolPosition = 'after',
		disabled = false,
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		mappingCreate: {
			lineItemId: string;
			mapping: {
				wbs_library_item_id: string;
				coverage_percentage?: number;
				coverage_quantity?: number;
				notes?: string;
			};
		};
		mappingUpdate: {
			mappingId: string;
			mapping: {
				coverage_percentage?: number;
				coverage_quantity?: number;
				notes?: string;
			};
		};
		mappingDelete: {
			mappingId: string;
		};
	}>();

	let addDialogOpen = $state(false);
	let editDialogOpen = $state(false);
	let selectedWbsItems = $state<string[]>([]);
	let searchQuery = $state('');
	let selectedMapping = $state<(typeof existingMappings)[0] | null>(null);

	// Form state for new mapping
	let newMappingForm = $state({
		coverageType: 'percentage' as 'percentage' | 'quantity',
		coveragePercentage: 100,
		coverageQuantity: null as number | null,
		notes: '',
	});

	// Form state for editing mapping
	let editMappingForm = $state({
		coverageType: 'percentage' as 'percentage' | 'quantity',
		coveragePercentage: 100,
		coverageQuantity: null as number | null,
		notes: '',
	});

	// Calculate total coverage for validation
	const totalCoverage = $derived.by(() => {
		const coverageByWbs = new SvelteMap<string, number>();

		existingMappings.forEach((mapping) => {
			const current = coverageByWbs.get(mapping.wbs_library_item_id) || 0;
			coverageByWbs.set(mapping.wbs_library_item_id, current + (mapping.coverage_percentage || 0));
		});

		return coverageByWbs;
	});

	// Get WBS item details by ID
	function getWbsItem(itemId: string): WbsItemWithBudget | undefined {
		return availableWbsItems.find((item) => item.wbs_library_item_id === itemId);
	}

	function openAddDialog() {
		selectedWbsItems = [];
		newMappingForm = {
			coverageType: 'percentage',
			coveragePercentage: 100,
			coverageQuantity: null,
			notes: '',
		};
		addDialogOpen = true;
	}

	function openEditDialog(mapping: (typeof existingMappings)[0]) {
		selectedMapping = mapping;
		editMappingForm = {
			coverageType: mapping.coverage_quantity ? 'quantity' : 'percentage',
			coveragePercentage: mapping.coverage_percentage || 100,
			coverageQuantity: mapping.coverage_quantity || null,
			notes: mapping.notes || '',
		};
		editDialogOpen = true;
	}

	function handleWbsSelection(event: CustomEvent<{ itemId: string; item: WbsItemWithBudget }>) {
		const { itemId } = event.detail;
		if (!selectedWbsItems.includes(itemId)) {
			selectedWbsItems = [itemId]; // Single selection for now
		}
	}

	function handleWbsDeselection(event: CustomEvent<{ itemId: string }>) {
		const { itemId } = event.detail;
		selectedWbsItems = selectedWbsItems.filter((id) => id !== itemId);
	}

	function handleCoverageChange(
		event: CustomEvent<{
			type: 'percentage' | 'quantity';
			percentage: number;
			quantity: number | null;
		}>,
	) {
		const { type, percentage, quantity } = event.detail;
		newMappingForm.coverageType = type;
		newMappingForm.coveragePercentage = percentage;
		newMappingForm.coverageQuantity = quantity;
	}

	function handleEditCoverageChange(
		event: CustomEvent<{
			type: 'percentage' | 'quantity';
			percentage: number;
			quantity: number | null;
		}>,
	) {
		const { type, percentage, quantity } = event.detail;
		editMappingForm.coverageType = type;
		editMappingForm.coveragePercentage = percentage;
		editMappingForm.coverageQuantity = quantity;
	}

	function createMapping() {
		if (selectedWbsItems.length === 0) return;

		const wbsItemId = selectedWbsItems[0];
		const mapping = {
			wbs_library_item_id: wbsItemId,
			coverage_percentage:
				newMappingForm.coverageType === 'percentage'
					? newMappingForm.coveragePercentage
					: undefined,
			coverage_quantity:
				newMappingForm.coverageType === 'quantity'
					? newMappingForm.coverageQuantity || undefined
					: undefined,
			notes: newMappingForm.notes || undefined,
		};

		dispatch('mappingCreate', { lineItemId, mapping });
		addDialogOpen = false;
	}

	function updateMapping() {
		if (!selectedMapping) return;

		const mapping = {
			coverage_percentage:
				editMappingForm.coverageType === 'percentage'
					? editMappingForm.coveragePercentage
					: undefined,
			coverage_quantity:
				editMappingForm.coverageType === 'quantity'
					? editMappingForm.coverageQuantity || undefined
					: undefined,
			notes: editMappingForm.notes || undefined,
		};

		dispatch('mappingUpdate', { mappingId: selectedMapping.tender_wbs_mapping_id, mapping });
		editDialogOpen = false;
	}

	function deleteMapping(mappingId: string) {
		dispatch('mappingDelete', { mappingId });
	}

	function getCoverageStatus(wbsItemId: string): 'complete' | 'partial' | 'over' {
		const coverage = totalCoverage.get(wbsItemId) || 0;
		if (coverage > 100) return 'over';
		if (coverage < 100) return 'partial';
		return 'complete';
	}

	function getCoverageVariant(status: 'complete' | 'partial' | 'over') {
		switch (status) {
			case 'complete':
				return 'default';
			case 'partial':
				return 'secondary';
			case 'over':
				return 'destructive';
		}
	}
</script>

<div class="space-y-4">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h3 class="text-lg font-semibold">WBS Mapping</h3>
			<p class="text-sm text-gray-600">Map this line item to WBS codes</p>
		</div>
		<Button onclick={openAddDialog} {disabled}>
			<PlusIcon class="mr-2 h-4 w-4" />
			Add Mapping
		</Button>
	</div>

	<!-- Existing Mappings -->
	{#if existingMappings.length > 0}
		<div class="space-y-3">
			{#each existingMappings as mapping (mapping.tender_wbs_mapping_id)}
				{@const wbsItem = getWbsItem(mapping.wbs_library_item_id)}
				{@const coverageStatus = getCoverageStatus(mapping.wbs_library_item_id)}

				<Card.Root>
					<Card.Content class="p-4">
						<div class="flex items-start justify-between">
							<div class="min-w-0 flex-1">
								<div class="mb-2 flex items-center space-x-2">
									<span class="font-mono text-sm font-medium">{wbsItem?.code || 'Unknown'}</span>
									<Badge variant={getCoverageVariant(coverageStatus)}>
										{mapping.coverage_percentage || 0}%
									</Badge>
								</div>
								<p class="mb-2 text-sm text-gray-600">
									{wbsItem?.description || 'Unknown WBS item'}
								</p>

								<div class="space-y-1 text-xs text-gray-500">
									{#if mapping.coverage_quantity}
										<div>Quantity: {mapping.coverage_quantity} {wbsItem?.unit || ''}</div>
									{/if}
									{#if wbsItem?.budget_amount}
										<div>
											Budget: {formatCurrency(wbsItem.budget_amount, {
												symbol: currencySymbol,
												symbolPosition,
												fallback: '-',
											})}
										</div>
									{/if}
									{#if mapping.notes}
										<div>Notes: {mapping.notes}</div>
									{/if}
								</div>
							</div>

							<div class="ml-4 flex space-x-1">
								<Button
									variant="ghost"
									size="sm"
									onclick={() => openEditDialog(mapping)}
									{disabled}
								>
									<PencilIcon class="h-4 w-4" />
								</Button>
								<Button
									variant="ghost"
									size="sm"
									onclick={() => deleteMapping(mapping.tender_wbs_mapping_id)}
									{disabled}
								>
									<TrashIcon class="h-4 w-4" />
								</Button>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	{:else}
		<div class="py-8 text-center text-gray-500">
			<p class="text-sm">No WBS mappings yet</p>
			<p class="text-xs">Add mappings to connect this line item to your project's WBS structure</p>
		</div>
	{/if}
</div>

<!-- Add Mapping Dialog -->
<Dialog.Root bind:open={addDialogOpen}>
	<Dialog.Content class="max-h-[90vh] max-w-4xl overflow-y-auto">
		<Dialog.Header>
			<Dialog.Title>Add WBS Mapping</Dialog.Title>
			<Dialog.Description>
				Map "{lineItemDescription}" to WBS items
			</Dialog.Description>
		</Dialog.Header>

		<div class="grid grid-cols-1 gap-6 py-4 lg:grid-cols-2">
			<!-- WBS Selection -->
			<div class="space-y-4">
				<Label class="text-sm font-medium">Select WBS Items</Label>
				<WbsTreeSelector
					wbsItems={availableWbsItems}
					bind:selectedItems={selectedWbsItems}
					bind:searchQuery
					allowMultiSelect={false}
					{currencySymbol}
					{symbolPosition}
					on:select={handleWbsSelection}
					on:deselect={handleWbsDeselection}
				/>
			</div>

			<!-- Coverage Configuration -->
			<div class="space-y-4">
				{#if selectedWbsItems.length > 0}
					{@const selectedWbsItem = getWbsItem(selectedWbsItems[0])}
					<div>
						<Label class="text-sm font-medium">Coverage Configuration</Label>
						<div class="mt-2 rounded-md bg-gray-50 p-3">
							<div class="text-sm font-medium">{selectedWbsItem?.code}</div>
							<div class="text-xs text-gray-600">{selectedWbsItem?.description}</div>
						</div>
					</div>

					<CoverageInput
						bind:coverageType={newMappingForm.coverageType}
						bind:coveragePercentage={newMappingForm.coveragePercentage}
						bind:coverageQuantity={newMappingForm.coverageQuantity}
						budgetQuantity={selectedWbsItem?.budget_quantity}
						budgetAmount={selectedWbsItem?.budget_amount}
						unit={selectedWbsItem?.unit}
						{currencySymbol}
						{symbolPosition}
						on:change={handleCoverageChange}
					/>

					<div class="space-y-2">
						<Label for="mapping-notes" class="text-sm font-medium">Notes (Optional)</Label>
						<Textarea
							id="mapping-notes"
							placeholder="Add notes about this mapping..."
							bind:value={newMappingForm.notes}
							rows={3}
						/>
					</div>
				{:else}
					<div class="py-8 text-center text-gray-500">
						<p class="text-sm">Select a WBS item to configure coverage</p>
					</div>
				{/if}
			</div>
		</div>

		<Dialog.Footer>
			<Button variant="outline" onclick={() => (addDialogOpen = false)}>Cancel</Button>
			<Button onclick={createMapping} disabled={selectedWbsItems.length === 0}>Add Mapping</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>

<!-- Edit Mapping Dialog -->
<Dialog.Root bind:open={editDialogOpen}>
	<Dialog.Content class="max-w-2xl">
		<Dialog.Header>
			<Dialog.Title>Edit WBS Mapping</Dialog.Title>
			<Dialog.Description>
				Update coverage for {selectedMapping?.wbs_library_item.code}
			</Dialog.Description>
		</Dialog.Header>

		{#if selectedMapping}
			{@const wbsItem = getWbsItem(selectedMapping.wbs_library_item_id)}
			<div class="space-y-4 py-4">
				<div class="rounded-md bg-gray-50 p-3">
					<div class="text-sm font-medium">{selectedMapping.wbs_library_item.code}</div>
					<div class="text-xs text-gray-600">{selectedMapping.wbs_library_item.description}</div>
				</div>

				<CoverageInput
					bind:coverageType={editMappingForm.coverageType}
					bind:coveragePercentage={editMappingForm.coveragePercentage}
					bind:coverageQuantity={editMappingForm.coverageQuantity}
					budgetQuantity={wbsItem?.budget_quantity}
					budgetAmount={wbsItem?.budget_amount}
					unit={wbsItem?.unit}
					{currencySymbol}
					{symbolPosition}
					on:change={handleEditCoverageChange}
				/>

				<div class="space-y-2">
					<Label for="edit-mapping-notes" class="text-sm font-medium">Notes (Optional)</Label>
					<Textarea
						id="edit-mapping-notes"
						placeholder="Add notes about this mapping..."
						bind:value={editMappingForm.notes}
						rows={3}
					/>
				</div>
			</div>
		{/if}

		<Dialog.Footer>
			<Button variant="outline" onclick={() => (editDialogOpen = false)}>Cancel</Button>
			<Button onclick={updateMapping}>Update Mapping</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>

import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import {
	createWbsMappingSchema,
	editWbsMappingSchema,
	budgetTransferSchema,
} from '$lib/schemas/tender';
import {
	tenderLineItemShortId,
	tenderLineItemUUID,
	tenderShortId,
	tenderUUID,
	projectShortId,
	projectUUID,
} from '$lib/schemas/project';
import {
	getLineItemById,
	createWbsMapping,
	updateWbsMapping,
	deleteWbsMapping,
	createBudgetTransfer,
	getProjectBudgetTransfers,
} from '$lib/tender_utils';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase, user } }) => {
	if (!user) {
		throw redirect(302, '/auth/signin');
	}

	const projectId = projectUUID(params.project_id_short);
	const tenderId = tenderUUID(params.tender_id_short);
	const lineItemId = tenderLineItemUUID(params.line_item_id_short);

	try {
		const { data: project, error: projectError } = await supabase
			.from('project')
			.select('project_id, name, active_budget_version_id')
			.eq('project_id', projectId)
			.single();

		if (projectError) {
			console.error('Error fetching project for WBS mapping:', projectError);
			throw error(500, 'Failed to load project data');
		}

		if (!project) {
			throw error(404, 'Project not found');
		}

		const activeBudgetVersionId = project.active_budget_version_id;

		// Get line item with existing mappings
		const lineItem = await getLineItemById(supabase, lineItemId);
		if (!lineItem) {
			throw error(404, 'Line item not found');
		}

		// Get project WBS items with budget information
		const wbsSelect = `
			*,
			budget_version_item (
				quantity,
				unit,
				unit_rate,
				factor,
				budget_version!inner (
					project_id,
					budget_version_id
				)
			)
		`;

		const baseWbsQuery = supabase
			.from('wbs_library_item')
			.select(wbsSelect)
			.eq('budget_version_item.budget_version.project_id', projectId)
			.order('code');

		const finalWbsQuery = activeBudgetVersionId
			? baseWbsQuery.eq(
					'budget_version_item.budget_version.budget_version_id',
					activeBudgetVersionId,
				)
			: baseWbsQuery;

		const { data: wbsItems, error: wbsError } = await finalWbsQuery;

		if (wbsError) {
			console.error('Error fetching WBS items:', wbsError);
			throw error(500, 'Failed to load WBS items');
		}

		// Transform WBS items to include budget information
		const wbsItemsWithBudget =
			wbsItems?.map((item) => {
				const budgetItem = item.budget_version_item?.[0];
				return {
					...item,
					budget_amount: budgetItem
						? (budgetItem.quantity || 0) * (budgetItem.unit_rate || 0) * (budgetItem.factor || 1)
						: 0,
					budget_quantity: budgetItem?.quantity || null,
					budget_unit_rate: budgetItem?.unit_rate || null,
					unit: budgetItem?.unit || null,
				};
			}) || [];

		// Get existing budget transfers for this project
		const budgetTransfers = await getProjectBudgetTransfers(supabase, projectId);

		// Initialize forms
		const createForm = await superValidate(zod(createWbsMappingSchema));
		const editForm = await superValidate(zod(editWbsMappingSchema));
		const transferForm = await superValidate(zod(budgetTransferSchema));

		return {
			lineItem,
			wbsItems: wbsItemsWithBudget,
			budgetTransfers,
			project,
			createForm,
			editForm,
			transferForm,
		};
	} catch (err) {
		console.error('Error loading WBS mapping page:', err);
		throw error(500, 'Failed to load page data');
	}
};

export const actions: Actions = {
	createMapping: async ({ request, params, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const lineItemId = tenderLineItemUUID(params.line_item_id_short);
		const form = await superValidate(request, zod(createWbsMappingSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			await createWbsMapping(supabase, lineItemId, {
				wbs_library_item_id: form.data.wbs_library_item_id,
				coverage_percentage: form.data.coverage_percentage,
				coverage_quantity: form.data.coverage_quantity,
				notes: form.data.notes,
			});

			return {
				form,
				message: { type: 'success', text: 'WBS mapping created successfully' },
			};
		} catch (err) {
			console.error('Error creating WBS mapping:', err);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create WBS mapping' },
			});
		}
	},

	updateMapping: async ({ request, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const form = await superValidate(request, zod(editWbsMappingSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const formData = await request.formData();
		const mappingId = formData.get('mapping_id') as string;

		if (!mappingId) {
			return fail(400, {
				form,
				message: { type: 'error', text: 'Mapping ID is required' },
			});
		}

		try {
			await updateWbsMapping(supabase, mappingId, {
				coverage_percentage: form.data.coverage_percentage,
				coverage_quantity: form.data.coverage_quantity,
				notes: form.data.notes,
			});

			return {
				form,
				message: { type: 'success', text: 'WBS mapping updated successfully' },
			};
		} catch (err) {
			console.error('Error updating WBS mapping:', err);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update WBS mapping' },
			});
		}
	},

	deleteMapping: async ({ request, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const formData = await request.formData();
		const mappingId = formData.get('mapping_id') as string;

		if (!mappingId) {
			return fail(400, {
				message: { type: 'error', text: 'Mapping ID is required' },
			});
		}

		try {
			await deleteWbsMapping(supabase, mappingId);

			return {
				message: { type: 'success', text: 'WBS mapping deleted successfully' },
			};
		} catch (err) {
			console.error('Error deleting WBS mapping:', err);
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete WBS mapping' },
			});
		}
	},

	createTransfer: async ({ request, params, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const projectId = projectUUID(params.project_id_short);
		const lineItemId = tenderLineItemUUID(params.line_item_id_short);
		const form = await superValidate(request, zod(budgetTransferSchema));

		if (!form.valid) {
			return fail(400, { transferForm: form });
		}

		try {
			// Use RPC function for validation and creation
			const { data, error: rpcError } = await supabase.rpc('create_budget_transfer', {
				project_id_param: projectId,
				line_item_id_param: lineItemId,
				from_wbs_item_id: form.data.from_wbs_library_item_id,
				to_wbs_item_id: form.data.to_wbs_library_item_id,
				transfer_amount: form.data.transfer_amount,
				reason: form.data.transfer_reason || 'Budget transfer for tender line item',
			});

			if (rpcError || !data?.[0]?.is_valid) {
				return fail(400, {
					transferForm: form,
					message: {
						type: 'error',
						text: data?.[0]?.error_message || 'Failed to create budget transfer',
					},
				});
			}

			return {
				transferForm: form,
				message: { type: 'success', text: 'Budget transfer created successfully' },
			};
		} catch (err) {
			console.error('Error creating budget transfer:', err);
			return fail(500, {
				transferForm: form,
				message: { type: 'error', text: 'Failed to create budget transfer' },
			});
		}
	},

	validateTransfer: async ({ request, params, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const projectId = projectUUID(params.project_id_short);
		const formData = await request.formData();

		const fromWbsItemId = formData.get('from_wbs_library_item_id') as string;
		const toWbsItemId = formData.get('to_wbs_library_item_id') as string;
		const transferAmount = parseFloat(formData.get('transfer_amount') as string);

		if (!fromWbsItemId || !toWbsItemId || !transferAmount) {
			return fail(400, {
				message: { type: 'error', text: 'All fields are required for validation' },
			});
		}

		try {
			const { data, error: rpcError } = await supabase.rpc('validate_budget_transfer', {
				project_id_param: projectId,
				from_wbs_item_id: fromWbsItemId,
				to_wbs_item_id: toWbsItemId,
				transfer_amount: transferAmount,
			});

			if (rpcError) {
				return fail(500, {
					message: { type: 'error', text: 'Failed to validate transfer' },
				});
			}

			return {
				validation: data[0],
				message: {
					type: data[0].is_valid ? 'success' : 'error',
					text: data[0].is_valid ? 'Transfer is valid' : data[0].error_message,
				},
			};
		} catch (err) {
			console.error('Error validating budget transfer:', err);
			return fail(500, {
				message: { type: 'error', text: 'Failed to validate transfer' },
			});
		}
	},
};
